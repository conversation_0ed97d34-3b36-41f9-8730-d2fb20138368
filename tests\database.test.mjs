/**
 * 🧪 INTEGRATION TEST: Database Connection - PostgreSQL Integration
 *
 * This test validates that our PostgreSQL database connection is working
 * correctly with minimal mocking following your TDD philosophy.
 *
 * 🎯 PURPOSE:
 * - Validate PostgreSQL connection
 * - Test real database operations
 * - Ensure Prisma client is working
 * - Follow minimal mocking approach (only mock logger)
 *
 * 🔄 MINIMAL MOCKING APPROACH:
 * ✅ REAL: Database operations, Prisma client, business logic
 * 🔧 MOCK: Logger only (external dependency)
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import prisma from '../db/index.mjs';

// Only mock the logger - everything else is real
jest.mock('../utils/logger.mjs', () => ({
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('🗄️ INTEGRATION: Database Connection - PostgreSQL Integration', () => {
  beforeAll(async () => {
    // Ensure we can connect to the database
    await prisma.$connect();
  });

  afterAll(async () => {
    // Clean up connection
    await prisma.$disconnect();
  });

  it('should connect to PostgreSQL database', async () => {
    // Test basic database connectivity
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(result).toBeDefined();
    expect(result[0].test).toBe(1);
  });

  it('should be able to query database schema', async () => {
    // Test that we can query the database structure
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
    `;
    expect(Array.isArray(tables)).toBe(true);
  });

  it('should validate Prisma client is working', async () => {
    // Test that Prisma client methods exist
    expect(prisma.$connect).toBeDefined();
    expect(prisma.$disconnect).toBeDefined();
    expect(prisma.$queryRaw).toBeDefined();
  });
});
