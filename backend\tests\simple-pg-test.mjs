/**
 * Simple PostgreSQL connection test
 */

import { describe, it, expect } from '@jest/globals';
import { Client } from 'pg';

describe('PostgreSQL Connection', () => {
  it('should connect to PostgreSQL', async () => {
    const client = new Client({
      connectionString: 'postgres://postgres:JimpkpNnVF2o%23DaX1Qx0@localhost:5432/equoria_test'
    });
    
    await client.connect();
    
    const result = await client.query('SELECT 1 as value');
    expect(result.rows[0].value).toBe(1);
    
    await client.end();
  });
});