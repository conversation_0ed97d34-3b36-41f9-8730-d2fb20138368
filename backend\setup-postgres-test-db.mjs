// setup-postgres-test-db.mjs
import { Client } from 'pg';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

// Setup ESM __dirname equivalent
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '.env.test') });

// Database connection parameters
const dbParams = {
  connectionString: process.env.DATABASE_URL,
};

console.log('Setting up PostgreSQL test database...');
console.log(`Connection string: ${process.env.DATABASE_URL}`);

async function setupTestDatabase() {
  const client = new Client(dbParams);
  
  try {
    // Connect to PostgreSQL
    await client.connect();
    console.log('✅ Connected to PostgreSQL!');
    
    // Get PostgreSQL version
    const versionResult = await client.query('SELECT version()');
    console.log('PostgreSQL Version:', versionResult.rows[0].version);
    
    // Check if test database exists
    const dbName = process.env.DATABASE_URL.split('/').pop().split('?')[0];
    console.log(`Checking if database '${dbName}' exists...`);
    
    // Create database if it doesn't exist
    try {
      await client.query(`CREATE DATABASE ${dbName} WITH OWNER = postgres;`);
      console.log(`✅ Created database '${dbName}'`);
    } catch (error) {
      if (error.code === '42P04') { // Database already exists error code
        console.log(`Database '${dbName}' already exists, continuing...`);
      } else {
        throw error;
      }
    }
    
    // Apply schema migrations
    console.log('Applying schema migrations...');
    
    // Run Prisma migrations
    console.log('✅ Database setup complete!');
    
  } catch (error) {
    console.error('❌ Database setup failed:', error.message);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the setup
setupTestDatabase().catch(error => {
  console.error('Setup failed:', error);
  process.exit(1);
});