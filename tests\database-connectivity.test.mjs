/**
 * 🧪 DATABASE CONNECTIVITY TEST: PostgreSQL Connection Validation
 *
 * This test validates that our PostgreSQL database connection is working
 * correctly and that the schema has been properly applied.
 *
 * 🎯 PURPOSE:
 * - Validate PostgreSQL connection
 * - Test that tables exist
 * - Ensure Prisma client is working
 * - Follow minimal mocking approach
 *
 * 🔄 MINIMAL MOCKING APPROACH:
 * ✅ REAL: Database operations, Prisma client, business logic
 * 🔧 MOCK: Logger only (external dependency)
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import prisma from '../db/index.mjs';

// Only mock the logger - everything else is real
jest.mock('../utils/logger.mjs', () => ({
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('🗄️ DATABASE CONNECTIVITY: PostgreSQL Integration', () => {
  beforeAll(async () => {
    // Ensure we can connect to the database
    await prisma.$connect();
  });

  afterAll(async () => {
    // Clean up connection
    await prisma.$disconnect();
  });

  it('should connect to PostgreSQL database', async () => {
    // Test basic database connectivity
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(result).toBeDefined();
    expect(result[0].test).toBe(1);
  });

  it('should have horses table', async () => {
    // Test that horses table exists
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'horses'
    `;
    expect(tables).toHaveLength(1);
    expect(tables[0].table_name).toBe('horses');
  });

  it('should have groom tables', async () => {
    // Test that groom-related tables exist
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name IN ('grooms', 'groom_assignments', 'groom_interactions')
      ORDER BY table_name
    `;
    expect(tables).toHaveLength(3);
    expect(tables.map(t => t.table_name)).toEqual([
      'groom_assignments',
      'groom_interactions', 
      'grooms'
    ]);
  });

  it('should validate Prisma client methods exist', async () => {
    // Test that Prisma client methods exist
    expect(prisma.$connect).toBeDefined();
    expect(prisma.$disconnect).toBeDefined();
    expect(prisma.$queryRaw).toBeDefined();
    expect(prisma.horse).toBeDefined();
    expect(prisma.groom).toBeDefined();
    expect(prisma.groomAssignment).toBeDefined();
  });
});
